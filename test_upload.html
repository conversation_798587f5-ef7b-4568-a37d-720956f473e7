<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Upload Image</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="file"] {
            width: 100%;
            padding: 10px;
            border: 2px dashed #ddd;
            border-radius: 5px;
        }
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .uploaded-image {
            max-width: 100%;
            margin-top: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Upload Image to Cloudinary</h1>
        
        <form id="uploadForm">
            <div class="form-group">
                <label for="token">Token (Bearer):</label>
                <input type="text" id="token" placeholder="Nhập token để test authentication">
            </div>
            
            <div class="form-group">
                <label for="imageFile">Chọn ảnh:</label>
                <input type="file" id="imageFile" accept="image/*" required>
            </div>
            
            <button type="submit" id="uploadBtn">Upload Image</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('imageFile');
            const tokenInput = document.getElementById('token');
            const uploadBtn = document.getElementById('uploadBtn');
            const resultDiv = document.getElementById('result');
            
            if (!fileInput.files[0]) {
                showResult('Vui lòng chọn file ảnh', 'error');
                return;
            }
            
            uploadBtn.disabled = true;
            uploadBtn.textContent = 'Đang upload...';
            resultDiv.innerHTML = '';
            
            const formData = new FormData();
            formData.append('image', fileInput.files[0]);
            
            const headers = {};
            if (tokenInput.value.trim()) {
                headers['Authorization'] = `Bearer ${tokenInput.value.trim()}`;
            }
            
            try {
                const response = await fetch('http://localhost/api/upload_image.php', {
                    method: 'POST',
                    body: formData,
                    headers: headers,
                    credentials: 'include'
                });
                
                const result = await response.text();
                
                try {
                    const jsonResult = JSON.parse(result);
                    if (jsonResult.success) {
                        showResult(`Upload thành công!\nURL: ${jsonResult.url}`, 'success');
                        showUploadedImage(jsonResult.url);
                    } else {
                        showResult(`Upload thất bại:\n${JSON.stringify(jsonResult, null, 2)}`, 'error');
                    }
                } catch (parseError) {
                    showResult(`Lỗi parse JSON:\nResponse: ${result}`, 'error');
                }
                
            } catch (error) {
                showResult(`Lỗi network: ${error.message}`, 'error');
            } finally {
                uploadBtn.disabled = false;
                uploadBtn.textContent = 'Upload Image';
            }
        });
        
        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }
        
        function showUploadedImage(url) {
            const resultDiv = document.getElementById('result');
            const img = document.createElement('img');
            img.src = url;
            img.className = 'uploaded-image';
            resultDiv.appendChild(img);
        }
        
        // Auto-fill token from localStorage if available
        const savedToken = localStorage.getItem('token');
        if (savedToken) {
            document.getElementById('token').value = savedToken;
        }
    </script>
</body>
</html>
